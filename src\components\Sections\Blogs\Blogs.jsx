import { Box, Container, Grid, Typography } from "@mui/material";
import BlogCard from "./BlogCard";

export default function Blogs() {
    return (
        <Box sx={{ py: { xs: 4, md: 8 } }}>
            <Container maxWidth="xl">
                <Typography
                    sx={{
                        color: 'primary.main',
                        fontWeight: 600,
                        textAlign: 'center',
                        fontSize: { xs: '14px', md: '16px' },
                        mb: 2,
                        letterSpacing: '0.5px'
                    }}
                >
                    Blog & News
                </Typography>
                <Typography
                    textAlign='center'
                    variant='h2'
                    sx={{
                        mb: { xs: 4, md: 6 },
                        fontSize: { xs: '32px', md: '48px' },
                        fontWeight: 600,
                        lineHeight: 1.2
                    }}
                >
                    Read Our Latest News
                </Typography>

                <Grid container spacing={{ xs: 2, md: 2 }}>
                    <Grid item xs={12} sm={4} md={4}>
                        <BlogCard />
                    </Grid>
                    <Grid item xs={12} sm={4} md={4}>
                        <BlogCard />
                    </Grid>
                    <Grid item xs={12} sm={4} md={4}>
                        <BlogCard />
                    </Grid>
                </Grid>
            </Container>
        </Box>
    )
}