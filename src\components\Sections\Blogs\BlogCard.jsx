import { Box, Stack, Typography } from "@mui/material";
import featured from '../../../assets/blog.png'
import person from '../../../assets/person.png'

export default function BlogCard() {
    return (
        <Box
            sx={{
                border: '1px solid rgba(0,0,0,0.1)',
                borderRadius: 2,
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                }
            }}
        >
            <Box
                component='img'
                src={featured}
                sx={{
                    width: '100%',
                    height: { xs: '160px', md: '160px' },
                    objectFit: 'cover'
                }}
            />
            <Box sx={{ p: { xs: 1.5, md: 2 }, flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Typography
                    sx={{
                        color: '#77829D',
                        fontWeight: 500,
                        mb: 1,
                        fontSize: { xs: '11px', md: '12px' }
                    }}
                >
                    Medical | March 31, 2022
                </Typography>
                <Typography
                    component='h3'
                    sx={{
                        color: '#1B3C74',
                        fontSize: { xs: '14px', md: '15px' },
                        fontWeight: 600,
                        lineHeight: 1.2,
                        mb: 1.5,
                        flex: 1,
                        minHeight: { xs: '36px', md: '36px' },
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                    }}
                >
                    6 Tips To Protect Your Mental Health When You're Sick
                </Typography>
                <Stack direction='row' spacing={1} alignItems='center' sx={{ mt: 'auto' }}>
                    <Box
                        component='img'
                        src={person}
                        sx={{
                            height: { xs: 20, md: 24 },
                            width: { xs: 20, md: 24 },
                            borderRadius: '50%'
                        }}
                    />
                    <Typography
                        sx={{
                            color: '#1B3C74',
                            fontSize: { xs: '12px', md: '13px' },
                            fontWeight: 500
                        }}
                    >
                        Rebecca Lee
                    </Typography>
                </Stack>
            </Box>
        </Box>
    )
}