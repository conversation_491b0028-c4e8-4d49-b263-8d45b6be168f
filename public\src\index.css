:root {
  --color-white: #ffffff;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

/*Custom Pagination*/
.swiper-pagination {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 28px;
  padding-bottom:5px;
}

.swiper-pagination-bullet {
  height: 10px;
  width: 10px;
  background: #BCD8E9;
  border-radius: 50%;
  cursor: pointer;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active{
  background: #2AA7FF;
  outline : 1px solid #2AA7FF;
  outline-offset: 4px;
}
